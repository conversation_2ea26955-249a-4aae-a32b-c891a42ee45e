import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { RemedinovaDataService } from '../../services/remedinova-data.service';
import { DataLoaderService } from '../../services/data-loader.service';

@Component({
  selector: 'app-table-navigator',
  templateUrl: './table-navigator.page.html',
  styleUrls: ['./table-navigator.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class TableNavigatorPage implements OnInit {
  
  isDataReady = false;
  isLoading = false;
  dbStats: any = {};
  totalRecords = 0;
  
  navigationOptions = [
    {
      title: 'Interactive Data Tables',
      subtitle: 'Browse and search individual tables with full data',
      icon: 'grid-outline',
      route: '/data-tables',
      color: 'primary',
      description: 'Click on any table to expand and view all records with search functionality'
    },
    {
      title: 'All Tables Overview',
      subtitle: 'View sample data from all 22 tables at once',
      icon: 'apps-outline',
      route: '/all-tables-view',
      color: 'secondary',
      description: 'See a comprehensive overview with sample data from every table'
    },
    {
      title: 'Data Demo & Testing',
      subtitle: 'Test search functionality and view statistics',
      icon: 'flask-outline',
      route: '/data-demo',
      color: 'tertiary',
      description: 'Interactive demo for testing search and data operations'
    }
  ];

  constructor(
    private router: Router,
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService
  ) {}

  async ngOnInit() {
    await this.initializeData();
  }

  async initializeData() {
    this.isLoading = true;
    
    try {
      // Ensure data is loaded
      await this.dataLoader.ensureDataLoaded();
      this.isDataReady = true;
      
      // Get database statistics
      this.dbStats = await this.remedinovaData.getDatabaseStats();
      this.totalRecords = Object.values(this.dbStats).reduce(
        (total: number, info: any) => total + (info.doc_count || 0), 
        0
      );
      
    } catch (error) {
      console.error('Error initializing data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }

  async reloadData() {
    this.isLoading = true;
    try {
      await this.dataLoader.reloadData();
      await this.initializeData();
    } catch (error) {
      console.error('Error reloading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getTopTables(): Array<{name: string, count: number}> {
    return Object.entries(this.dbStats)
      .map(([name, info]: [string, any]) => ({
        name: this.getDisplayName(name),
        count: info.doc_count || 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private getDisplayName(tableName: string): string {
    const displayNames: { [key: string]: string } = {
      'tblpatientcomplaints': 'Patient Complaints',
      'tblmedicinemaster': 'Medicine Master',
      'tbldiagnosismaster': 'Diagnosis Master',
      'tbllabsubtest': 'Lab Sub Tests',
      'tblstate': 'States',
      'tbldistrict': 'Districts',
      'tblvillage': 'Villages'
    };
    return displayNames[tableName] || tableName;
  }
}
