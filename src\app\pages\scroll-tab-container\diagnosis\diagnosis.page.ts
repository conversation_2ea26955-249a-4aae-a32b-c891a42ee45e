import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';

@Component({
  selector: 'app-diagnosis',
  templateUrl: './diagnosis.page.html',
  styleUrls: ['./diagnosis.page.scss'],
  standalone: true,
  imports: [IonicModule,  CommonModule, FormsModule]
})
export class DiagnosisPage implements OnInit {

  // ------------------ Diagnosis Tab ------------------
constructor(

) {}

  ngOnInit() {
    // Optional: Initialization logic here
  }
diagnosisMode: 'select' | 'chapter' | 'manual' = 'select';

selectedDiagnosis: string = '';
isProvisional: boolean = false;

diagnoses: { code: string; name: string; provisional: boolean }[] = [
  { code: 'A42.1', name: 'Abdominal Actinomycosis', provisional: true },
  { code: 'A19.0', name: 'Acute Miliary Tuberculosis Of A Single Specified Site', provisional: true },
  { code: 'J00', name: 'Acute Nasopharyngitis [Common Cold]', provisional: false },
  { code: 'Y45.5', name: '4-Aminophenol Derivatives', provisional: false }
];

addDiagnosis() {
  if (!this.selectedDiagnosis.trim()) {
    return;
  }

  const newDiagnosis = {
    code: this.generateICDCode(), // You could enhance this
    name: this.selectedDiagnosis,
    provisional: this.isProvisional
  };

  this.diagnoses.push(newDiagnosis);
  this.selectedDiagnosis = '';
  this.isProvisional = false;
}

editDiagnosis(item: any) {
  this.selectedDiagnosis = item.name;
  this.isProvisional = item.provisional;
  this.deleteDiagnosis(item); // Temporarily remove to re-add
}

deleteDiagnosis(item: any) {
  this.diagnoses = this.diagnoses.filter(d => d !== item);
}

generateICDCode(): string {
  // You can replace this with a real ICD lookup service
  const code = 'X' + Math.floor(100 + Math.random() * 900); // Mock code
  return code;
}


}
