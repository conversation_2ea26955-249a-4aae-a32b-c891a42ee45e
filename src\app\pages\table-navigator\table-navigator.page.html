<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>RemediNova Data Explorer</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="reloadData()" [disabled]="isLoading">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Data Explorer</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Initializing RemediNova database...</p>
  </div>

  <div *ngIf="!isLoading">
    <!-- Welcome Section -->
    <ion-card class="welcome-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="database-outline" class="title-icon"></ion-icon>
          Welcome to RemediNova Data Explorer
        </ion-card-title>
        <ion-card-subtitle>
          Explore and interact with your medical database containing 22 comprehensive tables
        </ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <div class="status-indicator" [class.ready]="isDataReady" [class.loading]="!isDataReady">
          <ion-icon [name]="isDataReady ? 'checkmark-circle' : 'time-outline'"></ion-icon>
          <span>{{ isDataReady ? 'Database Ready' : 'Loading Database...' }}</span>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Database Statistics -->
    <ion-card *ngIf="isDataReady" class="stats-card">
      <ion-card-header>
        <ion-card-title>Database Statistics</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="4">
              <div class="stat-item">
                <h2>22</h2>
                <p>Tables</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="stat-item">
                <h2>{{ totalRecords | number }}</h2>
                <p>Total Records</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="stat-item">
                <h2>{{ getDbStatsCount() }}</h2>
                <p>Loaded Tables</p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>

        <!-- Top Tables -->
        <div class="top-tables" *ngIf="getTopTables().length > 0">
          <h4>Largest Tables</h4>
          <ion-list>
            <ion-item *ngFor="let table of getTopTables()">
              <ion-label>
                <h3>{{ table.name }}</h3>
                <p>{{ table.count | number }} records</p>
              </ion-label>
              <ion-badge slot="end" color="primary">{{ table.count | number }}</ion-badge>
            </ion-item>
          </ion-list>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Navigation Options -->
    <div class="navigation-section">
      <h2 class="section-title">Choose Your View</h2>

      <div class="navigation-cards">
        <ion-card
          *ngFor="let option of navigationOptions"
          class="nav-card"
          [class]="'nav-card-' + option.color"
          button
          (click)="navigateTo(option.route)">

          <ion-card-header>
            <div class="nav-card-header">
              <ion-icon [name]="option.icon" [color]="option.color" class="nav-icon"></ion-icon>
              <div class="nav-info">
                <ion-card-title>{{ option.title }}</ion-card-title>
                <ion-card-subtitle>{{ option.subtitle }}</ion-card-subtitle>
              </div>
              <ion-icon name="chevron-forward-outline" class="nav-arrow"></ion-icon>
            </div>
          </ion-card-header>

          <ion-card-content>
            <p>{{ option.description }}</p>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- Quick Actions -->
    <ion-card class="actions-card">
      <ion-card-header>
        <ion-card-title>Quick Actions</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="action-buttons">
          <ion-button
            (click)="navigateTo('/data-tables')"
            expand="block"
            fill="outline"
            class="action-btn">
            <ion-icon name="grid-outline" slot="start"></ion-icon>
            Browse All Tables
          </ion-button>

          <ion-button
            (click)="navigateTo('/all-tables-view')"
            expand="block"
            fill="outline"
            class="action-btn">
            <ion-icon name="apps-outline" slot="start"></ion-icon>
            View All Data
          </ion-button>

          <ion-button
            (click)="reloadData()"
            expand="block"
            fill="clear"
            class="action-btn"
            [disabled]="isLoading">
            <ion-icon name="refresh-outline" slot="start"></ion-icon>
            Reload Database
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Information Footer -->
    <ion-card class="info-footer">
      <ion-card-content>
        <div class="info-content">
          <ion-icon name="information-circle-outline" class="info-icon"></ion-icon>
          <div class="info-text">
            <h4>About This Database</h4>
            <p>
              This application contains comprehensive medical data including patient complaints,
              medicines, diagnoses, location hierarchies, lab tests, and more. All data is stored
              locally using PouchDB for offline access and fast performance.
            </p>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
