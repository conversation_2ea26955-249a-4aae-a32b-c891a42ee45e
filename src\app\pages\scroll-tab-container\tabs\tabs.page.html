 <div class="scroll-tabs-wrapper">
          <div class="tab-container"></div>
          <div class="tab-header">
            <button (click)="switchTab('patientHistory')" class="button-native">Patient History</button>
            <button (click)="switchTab('complaints')" class="button-native">Complaints</button>
            <button (click)="switchTab('pastRecords')" class="button-native">Past Records</button>
            <button (click)="switchTab('parameters')" class="button-native">Parameters</button>
            <button (click)="switchTab('diagnosis')" class="button-native">Diagnosis</button>
            <button (click)="switchTab('medicines')" class="button-native">Medicines</button>
            <button (click)="switchTab('intervention')" class="button-native">Intervention</button>
            <button (click)="switchTab('investigations')" class="button-native">Investigations</button>
            <button (click)="switchTab('counseling')" class="button-native">Counseling</button>
            <button (click)="switchTab('referrals')" class="button-native">Referrals</button>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" [attr.data-selected-tab]="selectedTab">
          <ng-container [ngSwitch]="selectedTab">

            <div *ngSwitchCase="'patientHistory'" class="tab-panel">

                <app-patient-history></app-patient-history>

            </div>

            <!-- --------------------------------- Complaints -----------------------------  -->

            <div *ngSwitchCase="'complaints'" class="tab-panel">

              <div style="margin: 0;" class="cards">

                <div class=" ion-inherit-color">Complaints</div>


                <div>
                  <!-- Chief Complaint Input Row -->
                  <div class="complaint-form-row">
                    <div>
                      <label for="">Chief Complaints</label>

                      <input placeholder="Add Chief Complaints" class="complaint-input">

                    </div>

                    <div style="margin-left: 63px;">
                      <label for="">Associated Complaint</label>

                      <select placeholder="Select or Enter Associated Complaints" class="complaint-input">
                        <option value="minutes" disabled>Associated Complaint</option>
                      </select>

                    </div>

                  </div>

                  <!-- Associated Complaint Input Row -->
                  <div class="complaint-form-row">
                    <div style="display: flex; gap: 16px;margin-left: 19px;">
                      <div>
                        <label for="">Since</label>
                        <div class="complaint-input-2">
                          <input placeholder="Type Input in Digits">
                        </div>
                      </div>

                      <div>
                        <label for="">Time Period</label>
                        <div class="complaint-input-2">
                          <select placeholder="Select Time Period">
                            <option value="minutes">Minutes</option>
                            <option value="hours">Hours</option>
                            <option value="days">Days</option>
                          </select>
                        </div>
                      </div>

                      <button fill="clear" class="btn-add">
                        <img src="assets/icon/plus.png" alt="">
                        <span> Add</span>
                      </button>
                    </div>
                    <!-- -------------------  -->
                    <div style="display: flex;gap: 16px;">
                      <div>
                        <label for="">Since</label>
                        <div class="complaint-input-2">
                          <ion-input placeholder="Type Input in Digits"></ion-input>
                        </div>
                      </div>

                      <div>
                        <label for="">Time Period</label>
                        <div class="complaint-input-2">
                          <select placeholder="Select Time Period">
                            <option value="minutes">Minutes</option>
                            <option value="hours">Hours</option>
                            <option value="days">Days</option>
                          </select>
                        </div>
                      </div>
                      <button fill="clear" class="btn-add">
                        <img src="assets/icon/plus.png" alt="">
                        <span> Add</span>
                      </button>
                    </div>

                  </div>



                  <!-- Complaints Table -->

                </div>
              </div>
              <table class="complaints-table">
                <thead>
                  <tr>
                    <th>SNOMED CT</th>
                    <th>Complaint Texting</th>
                    <th>Since</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="highlight">
                    <td>-</td>
                    <td>Breathing Too Fast</td>
                    <td>2 Hours</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>271835004</td>
                    <td>Abdominal Swelling</td>
                    <td>2 Hours</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>12560004</td>
                    <td>Pain In The Fractured Part</td>
                    <td>2 Days</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>386661008, 82272006</td>
                    <td>Fever And Cold</td>
                    <td>5 Hours</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>




              <!-- ------------------------------  -->

            </div>

            <!-- -------------- pastRecords ----------------------  -->

            <div *ngSwitchCase="'pastRecords'" class="tab-panel">
              <!-- ------------  -->
              <div class="past-records-card cards" style="margin: 0;">
                <ion-card-header>
                  <div class=" ion-inherit-color">Past Records</div>
                  <div class="filter-actions">
                    <div class="chip-container">
                      <div class="record-noti">
                        <label>1st May July, 2025 - 6th May, 2025 </label>
                        <span (click)="removeFilter('date')"><img src="assets/icon/cross.png" alt=""></span>
                      </div>

                      <div class="record-noti">
                        <label>Newest First </label>
                        <span (click)="removeFilter('sort')"><img src="assets/icon/cross.png" alt=""></span>
                      </div>
                    </div>

                    <div class="icons-container">
                      <button fill="clear" size="small">
                        <img src="assets/icon/filter.png" alt=""><span class="filters-text"> Filters</span>
                      </button>
                      <button fill="clear" size="small">
                        <img src="assets/icon/upload.png" alt=""><span class="filters-text">Upload</span>
                      </button>
                    </div>
                  </div>
                </ion-card-header>
              </div>
              <!-- --------------- table  -->
              <table class="new-past-table" style="margin-top: 6px;">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Past Records</th>
                    <th>Attended By</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:15 AM</td>
                    <td>General Xray (1 Image)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:30 AM</td>
                    <td>Dermatoscope (4 Images)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:45 AM</td>
                    <td>ECG (1 Image)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>10:00 AM</td>
                    <td>Report No. 124</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>10:30 AM</td>
                    <td>Report No. 122</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>

                  </tr>
                </tbody>
              </table>
              <!-- ---------------- button -->
              <div>

                <div class="pagination-container">
                  <select interface="popover" placeholder="05 per page" [(ngModel)]="pageSize">
                    <option *ngFor="let size of pageSizes" [value]="size">{{ size }} per
                      page</option>
                  </select>

                  <div class="page-btn">

                    <div class="page-info">
                      {{ currentPageRange }} of {{ totalItems }}
                    </div>

                    <span style="border-left: 1px solid rgb(236, 236, 236);margin-left: 7px;
    height: 43px;" [ngClass]="{ 'disabled': currentPage === 1 }" (click)="currentPage !== 1 && previousPage()">
                      <img src="assets/icon/left-arraw.png" alt="">
                    </span>

                    <span style="height: 43px;" [ngClass]="{ 'disabled': currentPage === totalPages } "
                      (click)="currentPage !== totalPages && nextPage()">
                      <img src="assets/icon/light-arraw.png" alt="">
                    </span>

                  </div>
                </div>
              </div>

              <!-- -------------  -->
            </div>

            <!-- -------------------------- parameters -------------------  -->
            <div *ngSwitchCase="'parameters'" class="tab-panel">

              <div class="parameters-container cards">
                <div class=" ion-inherit-color">parameters</div>
                <!-- Legend -->
                <div class="legend">
                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #007AFF;"></span>
                    <span class="status-label">Connected</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #F59E0B;"></span>
                    <span class="status-label">disconnected</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #10B981;"></span>
                    <span class="status-label">Completed</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator"></span>
                    <span class="status-label">Disabled</span>
                  </button>

                  <span class="ble-status">BLE Dongle Connected</span>
                </div>

                <!-- Section Title -->
                <h3 class="section-title section-title2">Physiology Tests</h3>

                <!-- Cards Grid -->
                <div class="card-grid">
                  <!-- Sample Card -->
                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/temp.png" alt=""> <img src="assets/icon/sign.png"
                        alt="" class="img2"></div>
                    <div class="card-title">Temperature</div>
                    <div class="card-value">98.6°F</div>
                  </div>

                  <div class="card disconnected">
                    <div class="card-icon"><img src="assets/icon/temp.png" alt="" class="img1"> <img
                        src="assets/icon/unplug.png" alt="" class="img2"></div>
                    <div class="card-title">Temperature (ASHA+)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/stethoscope.png" alt="" class="img1"> <img
                        src="assets/icon/sign.png" alt="" class="img2"></div>
                    <div class="card-title">Stethoscope</div>
                    <div class="card-value">Normal</div>
                  </div>

                  <div class="card disconnected">
                    <div class="card-icon"><img src="assets/icon/stethoscope.png" alt="" class="img1"> <img
                        src="assets/icon/unplug.png" alt="" class="img2"></div>
                    <div class="card-title">Stethoscope (ASHA+)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/spirometer.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Spirometer</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/bloodpressure.png" alt=""> <img
                        src="assets/icon/sign.png" alt="" class="img2"></div>
                    <div class="card-title">Blood Pressure</div>
                    <div class="card-value">120/80 mmHg</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/spo.png" alt=""> <img src="assets/icon/sign.png" alt=""
                        class="img2"></div>
                    <div class="card-title">Spo2</div>
                    <div class="card-value">98%</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/ecg.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">ECG</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card disabled">
                    <div class="card-icon"><img src="assets/icon/ECGInterpretation.png" alt="" class="img1">
                      <img src="assets/icon/disable.png" alt="" class="img2">
                    </div>
                    <div class="card-title">ECG Interpretation</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/fetaldoppler.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Fetal Doppler (FD)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/fetaldoppler.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Fetal Doppler (Fetosense)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/refractometer.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Auto Refractometer</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/xrays.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">X-Ray</div>
                    <div class="card-value">-</div>
                  </div>

                </div>
                <h3 class="section-title">Blood And Urine Tests</h3>
                <!-- Cards Grid -->
                <div class="card-grid">
                  <!-- Sample Card -->
                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/glucose.png" alt=""> <img src="assets/icon/plug.png"
                        alt="" class="img2"></div>
                    <div class="card-title">Glucose</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/glucose.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Glucose (Wireless)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/hemoglobin.png" alt="" class="img1"><img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Hemoglobin</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/lipidprofile.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Lipid Profile</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/OpticalReader.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Optical Reader</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/urin.png" alt=""><img src="assets/icon/plug.png" alt=""
                        class="img2"></div>
                    <div class="card-title">Urine Test</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/bloodpressure.png" alt=""><img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">HbA1c (Wireless)</div>
                    <div class="card-value">-</div>
                  </div>
                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/hemoglobin.png" alt=""><img src="assets/icon/plug.png"
                        alt="" class="img2"></div>
                    <div class="card-title">WBC Differential</div>
                    <div class="card-value">-</div>
                  </div>





                </div>
              </div>


            </div>


            <!-- ---------------------- diagnosis  -->
            <ng-container *ngSwitchCase="'diagnosis'">
             <app-diagnosis></app-diagnosis>
            </ng-container>

            <!-- ----------------------------  --> <!-- medicines -->
            <ng-container *ngSwitchCase="'medicines'">
              <div class="medicines-section">
                <div class="medicines-header">
                  <div class=" ion-inherit-color">Medicines</div>
                  <a class="previous-prescription" href="#">
                    <span class="icon">&#8635;</span>
                    Previous Prescription
                  </a>
                </div>
                <div class="medicines-cont">
                  <form class="medicines-form">
                    <div class="form-group">
                      <label class="form-label">Drug Form <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Drug Form</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Medicine <span class="required">*</span></label>
                      <input class="form-control" type="text" placeholder="Enter Medicine" required />
                    </div>
                    <div class="form-group">
                      <label class="form-label">Instructions <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Instructions</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Dosage <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Dosage</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Frequency <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Frequency</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Days <span class="required">*</span></label>
                      <input type="text" class="form-select" placeholder="Enter Days*" required>

                    </div>
                    <!-- <div class="form-group add-btn-group">
          <label class="form-label">Days <span class="required">*</span></label>
          <input class="form-control" type="" placeholder="Enter Days" required />
      </div> -->

                  </form>
                  <button type="button" class="add-btn" style="margin-bottom: 31px;">
                    <span class="icon"><img src="assets/icon/plus.png" alt=""></span>
                    Add
                  </button>
                </div>
              </div>
              <!-- --------- table  -->
              <table class="new-complaints-table">
                <thead>
                  <tr>
                    <th>ICD Code</th>
                    <th>Diagnosis</th>
                    <th>Provisional</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>A42.1</td>
                    <td>Abdominal Actinomycosis</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>A19.0</td>
                    <td>Acute Miliary Tuberculosis Of A Single Specified Site</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>J00</td>
                    <td>Acute Nasopharyngitis [Common Cold]</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Y45.5</td>
                    <td>4-Aminophenol Derivatives</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </ng-container>
            <!-- -------------------------  -->


            <!-- --------------- intervention -->
            <ng-container *ngSwitchCase="'intervention'">
              <div class="intervention-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Intervention</div>
                </ion-card-header>

                <div>
                  <div class="intervention-grid">

                    <div class="intervention-group">
                      <label class="intervention-label">Treatment Plan</label>
                      <div class="plan-container">
                        <textarea class="plan-container" placeholder="Type the treatment plan *"
                          [(ngModel)]="intervention.treatmentPlan" class="intervention-textarea med-input"></textarea>
                      </div>
                    </div>

                    <div class="intervention-group">
                      <label class="intervention-label">Clinical Observations</label>
                      <div class="plan-container">
                        <textarea type="text" placeholder="Type the Clinical Observations *"
                          [(ngModel)]="intervention.clinicalObservations"
                          class="intervention-textarea med-input"></textarea>
                      </div>
                    </div>

                  </div>

                  <div class="intervention-followup">
                    <label class="intervention-label">Follow-Up Schedule</label>
                    <input type="date" [(ngModel)]="intervention.followUpDate"
                      class="intervention-date native-date-input med-input" style="margin-top: 14px;" />
                  </div>

                </div>
              </div>
            </ng-container>

            <!-- ---------- Investigations -----------  -->

            <div *ngSwitchCase="'investigations'" class="tab-panel">
              <div class="diagnosis-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Investigations</div>
                </ion-card-header>
                <div>

                  <ion-label class="diagnosis-header">How would you like to add Investigations?</ion-label>

                  <div class="select-diagnosis">
                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="select">
                      <label>Select Test</label>
                    </span>

                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="chapter">
                      <label>Select Lab and Test</label>
                    </span>


                  </div>
                  <div class="diagnosis-input-group">
                    <div class="diagnosis-checkbox">
                      <ion-label>Test</ion-label>
                      <div>
                        <input type="checkbox" [(ngModel)]="isProvisional" style="margin-right: 10px;">
                        <ion-label>Provisional</ion-label>
                      </div>
                    </div>

                    <div class="dig-input-contain">

                      <input type="text" class="diagnosis-input" [(ngModel)]="selectedDiagnosis"
                        placeholder="Select or Enter Test">

                      <span (click)="addDiagnosis()">+ Add</span>
                    </div>
                  </div>

                </div>
              </div>

              <!-- ---------- table  -->
              <table class="lab-table">
                <thead>
                  <tr>
                    <th>Lab</th>
                    <th>Test</th>
                    <th>Mandatory</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Not Available</td>
                    <td>Clotting Time</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>ABC Diagnostic Center</td>
                    <td>Complete Blood Count (CBC)</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Apollo Clinic</td>
                    <td>Urine Test</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Clinical Biochemistry</td>
                    <td>Prothrombin Time</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

            </div>

            <!-- --------------------------- consunseling -----------------------  -->
            <div *ngSwitchCase="'counseling'" class="tab-panel">
              <div class="medicines-section">
                <div class="medicines-header">
                  <div class=" ion-inherit-color">Counseling</div>
                  <a class="previous-prescription" href="#">
                    <span class="icon"></span>

                  </a>
                </div>
                <div class="medicines-cont">
                  <form class="medicines-form" style="grid-template-columns: repeat(2, 1fr)">


                    <div class="form-group">
                      <label class="form-label">Category<span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Category</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Counseling <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select or Type Counseling</option>
                      </select>
                    </div>

                    <!-- <div class="form-group add-btn-group">
          <label class="form-label">Days <span class="required">*</span></label>
          <input class="form-control" type="" placeholder="Enter Days" required />
      </div> -->

                  </form>
                  <button type="button" class="add-btn" style="align-items: flex-start; margin-top :30px;">
                    <span class="icon"><img src="assets/icon/plus.png" alt=""></span>
                    Add
                  </button>
                </div>
              </div>
              <!-- ----------------- table  -->
              <table class="lab-table">
                <thead>
                  <tr>
                    <th>Category</th>
                    <th>Counseling</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Dental</td>
                    <td>Brush twice daily</td>

                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Anemia</td>
                    <td>Have a piece of jaggery daily</td>

                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Acute gastroenteritis</td>
                    <td>Take plenty of oral fluids, Oral Rehydration Salts (ORS), lemonade, etc.</td>

                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Bronchitis and Asthma</td>
                    <td>Avoid any other allergens</td>

                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- ----------------------- referrals -----------------  -->
            <div *ngSwitchCase="'referrals'" class="tab-panel">
              <div class="medicines-section">
                <div class="medicines-header">
                  <div class=" ion-inherit-color">Referrals</div>
                  <a class="previous-prescription" href="#">
                    <span class="icon"></span>

                  </a>
                </div>
                <div class="medicines-cont">
                  <form class="medicines-form" style="grid-template-columns: repeat(1, 1fr)">


                    <div class="form-group">
                      <label class="form-label">Specialization <span class="required">*</span></label>
                      <select class="form-select" required>
                        <option value="" disabled selected>Select Specialization</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Referral Note <span class="required">*</span></label>
                      <input type="text" class="form-select" required placeholder="Type Referral Note *"
                        style="background: none; height: 96px;">

                    </div>

                    <!-- <div class="form-group add-btn-group">
                    <label class="form-label">Days <span class="required">*</span></label>
                      <input class="form-control" type="" placeholder="Enter Days" required />
                     </div> -->

                  </form>
                  <button type="button" class="add-btn" style="align-items: flex-end; margin-bottom :30px;">
                    <span class="icon"><img src="assets/icon/plus.png" alt=""></span>
                    Add
                  </button>
                </div>
              </div>
              <!-- ------------- table  -->

              <table class="referral-table">
                <thead>
                  <tr>
                    <th>Specialization</th>
                    <th>Referral Note</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Anesthesia</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Cardiac Surgery</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Dermatology</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>ENT</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </ng-container>
