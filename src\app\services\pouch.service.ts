import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

// Enable pouchdb-find plugin
PouchDB.plugin(PouchDBFind);

@Injectable({ providedIn: 'root' })
export class PouchService {
  private db: PouchDB.Database;

  constructor() {
    // Create or open the PouchDB database (in browser: uses IndexedDB)
    this.db = new PouchDB('remedinova');
  }

  /**
   * Ensures that initial JSON data is loaded and split into documents.
   * Only runs once (if no documents exist).
   */
  async ensureSeededFromAssets(): Promise<void> {
    const info = await this.db.info();
    if (info.doc_count > 0) {
      console.log('[PouchDB] Already seeded.');
      return; // already seeded
    }

    console.log('[PouchDB] Seeding from JSON...');

    const res = await fetch('assets/data/RemediNovaAPI.json');
    const json = await res.json();

    const docs: PouchDB.Core.PutDocument<any>[] = [];

    // Handle the nested structure where data is an array containing objects
    if (json.data && Array.isArray(json.data)) {
      json.data.forEach((dataItem: any) => {
        for (const [key, value] of Object.entries(dataItem)) {
          if (Array.isArray(value)) {
            value.forEach((item, index) => {
              const id = item.Id ?? item.id ?? `${index + 1}`;
              docs.push({
                _id: `${key}:${id}`,
                type: key,
                ...item
              });
            });
          } else if (typeof value === 'object' && value !== null) {
            docs.push({
              _id: `${key}:1`,
              type: key,
              ...value
            });
          } else {
            docs.push({
              _id: `meta:${key}`,
              type: 'meta',
              value
            });
          }
        }
      });
    } else {
      // Fallback to original logic for flat structure
      for (const [key, value] of Object.entries(json)) {
        if (Array.isArray(value)) {
          value.forEach((item, index) => {
            const id = item.Id ?? item.id ?? `${key}:${index + 1}`;
            docs.push({
              _id: `${key}:${id}`,
              type: key,
              ...item
            });
          });
        } else if (typeof value === 'object' && value !== null) {
          docs.push({
            _id: `${key}:1`,
            type: key,
            ...value
          });
        } else {
          docs.push({
            _id: `meta:${key}`,
            type: 'meta',
            value
          });
        }
      }
    }

    const result = await this.db.bulkDocs(docs);
    const failed = result.filter((r: any) => r.error);
    if (failed.length > 0) {
      console.warn('[PouchDB] Some docs failed to save:', failed);
    } else {
      console.log(`[PouchDB] Seeded ${docs.length} documents.`);
    }
  }

  /**
   * Retrieves documents by their `type` field (e.g., 'tblpatientcomplaints').
   * Automatically creates an index on `type` if needed.
   */
  async findByType(type: string): Promise<any[]> {
    await this.db.createIndex({ index: { fields: ['type'] } });

    const result = await this.db.find({
      selector: { type },
      sort: ['_id'],
      limit: 1000 // adjust as needed
    });

    return result.docs;
  }

  /**
   * Optional: Clear all documents in the database (for testing).
   */
  async clearAll(): Promise<void> {
    const all = await this.db.allDocs({ include_docs: true });
    const deletions = all.rows.map(row => ({
      _id: row.id,
      _rev: row.doc!._rev,
      _deleted: true
    }));
    await this.db.bulkDocs(deletions);
    console.log('[PouchDB] Cleared all data.');
  }

  /**
   * Returns basic DB info (doc count, backend, etc.)
   */
  async getInfo(): Promise<PouchDB.Core.DatabaseInfo> {
    return this.db.info();
  }
}
