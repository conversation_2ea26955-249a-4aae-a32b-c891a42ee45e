/**
 * Example of how to integrate RemediNova data services into your app
 * 
 * This file shows different ways to use the services in your components
 */

import { Component, OnInit } from '@angular/core';
import { DataLoaderService } from '../services/data-loader.service';
import { RemedinovaDataService, PatientComplaint, Medicine } from '../services/remedinova-data.service';
import { DataInitializer } from '../services/init-data';
import { PouchService } from '../services/pouch.service';

// Example 1: Basic App Component Integration
@Component({
  selector: 'app-root',
  template: `
    <ion-app>
      <ion-router-outlet *ngIf="isDataReady; else loading"></ion-router-outlet>
      
      <ng-template #loading>
        <ion-content class="loading-screen">
          <div class="loading-container">
            <ion-spinner name="crescent"></ion-spinner>
            <h2>Loading RemediNova Data...</h2>
            <p>{{ loadingMessage }}</p>
            <ion-progress-bar [value]="loadingProgress"></ion-progress-bar>
          </div>
        </ion-content>
      </ng-template>
    </ion-app>
  `
})
export class AppComponent implements OnInit {
  isDataReady = false;
  loadingMessage = 'Initializing...';
  loadingProgress = 0;

  constructor(
    private dataLoader: DataLoaderService,
    private remedinovaData: RemedinovaDataService,
    private pouchService: PouchService
  ) {}

  async ngOnInit() {
    await this.initializeApp();
  }

  private async initializeApp() {
    try {
      this.loadingMessage = 'Loading database...';
      this.loadingProgress = 0.2;

      // Initialize data
      const dataInitializer = new DataInitializer(
        this.dataLoader, 
        this.remedinovaData, 
        this.pouchService
      );

      this.loadingMessage = 'Seeding tables...';
      this.loadingProgress = 0.5;

      const result = await dataInitializer.initializeWithStats();

      if (result.success) {
        this.loadingMessage = 'Validating data...';
        this.loadingProgress = 0.8;

        const validation = await dataInitializer.validateData();
        
        if (validation.isValid) {
          this.loadingMessage = 'Ready!';
          this.loadingProgress = 1.0;
          
          console.log(`✅ App initialized with ${result.totalDocuments} documents in ${result.loadTime}ms`);
          
          setTimeout(() => {
            this.isDataReady = true;
          }, 500);
        } else {
          console.warn('⚠️ Data validation issues:', validation.issues);
          this.isDataReady = true; // Continue anyway
        }
      } else {
        throw new Error(result.error || 'Failed to initialize data');
      }

    } catch (error) {
      console.error('❌ App initialization failed:', error);
      this.loadingMessage = 'Failed to load data. Please refresh.';
      // You might want to show an error dialog here
    }
  }
}

// Example 2: Patient Complaint Selection Component
@Component({
  selector: 'app-complaint-selector',
  template: `
    <ion-searchbar 
      [(ngModel)]="searchText" 
      (ionInput)="onSearch()" 
      placeholder="Search complaints..."
      debounce="300">
    </ion-searchbar>
    
    <ion-list>
      <ion-item 
        *ngFor="let complaint of filteredComplaints" 
        button 
        (click)="selectComplaint(complaint)">
        <ion-label>
          <h3>{{ complaint.ComplaintName }}</h3>
          <p *ngIf="complaint.ReMeDi_Code">Code: {{ complaint.ReMeDi_Code }}</p>
        </ion-label>
      </ion-item>
    </ion-list>
  `
})
export class ComplaintSelectorComponent implements OnInit {
  searchText = '';
  allComplaints: PatientComplaint[] = [];
  filteredComplaints: PatientComplaint[] = [];

  constructor(private remedinovaData: RemedinovaDataService) {}

  async ngOnInit() {
    // Load all complaints on init
    this.allComplaints = await this.remedinovaData.getAllComplaints();
    this.filteredComplaints = this.allComplaints.slice(0, 20); // Show first 20
  }

  async onSearch() {
    if (this.searchText.trim()) {
      // Use search service for better performance
      this.filteredComplaints = await this.remedinovaData.searchComplaints(this.searchText);
    } else {
      // Show first 20 when no search
      this.filteredComplaints = this.allComplaints.slice(0, 20);
    }
  }

  selectComplaint(complaint: PatientComplaint) {
    console.log('Selected complaint:', complaint);
    // Emit selection or navigate
  }
}

// Example 3: Location Hierarchy Component
@Component({
  selector: 'app-location-selector',
  template: `
    <ion-item>
      <ion-label>Country</ion-label>
      <ion-select [(ngModel)]="selectedCountryId" (ionChange)="onCountryChange()">
        <ion-select-option *ngFor="let country of countries" [value]="country.CountryId">
          {{ country.Country }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <ion-item *ngIf="selectedCountryId">
      <ion-label>State</ion-label>
      <ion-select [(ngModel)]="selectedStateId" (ionChange)="onStateChange()">
        <ion-select-option *ngFor="let state of states" [value]="state.StateId">
          {{ state.State }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <ion-item *ngIf="selectedStateId">
      <ion-label>District</ion-label>
      <ion-select [(ngModel)]="selectedDistrictId" (ionChange)="onDistrictChange()">
        <ion-select-option *ngFor="let district of districts" [value]="district.DistrictId">
          {{ district.District }}
        </ion-select-option>
      </ion-select>
    </ion-item>
  `
})
export class LocationSelectorComponent implements OnInit {
  countries: any[] = [];
  states: any[] = [];
  districts: any[] = [];
  
  selectedCountryId = '';
  selectedStateId = '';
  selectedDistrictId = '';

  constructor(private remedinovaData: RemedinovaDataService) {}

  async ngOnInit() {
    this.countries = await this.remedinovaData.getAllCountries();
  }

  async onCountryChange() {
    this.selectedStateId = '';
    this.selectedDistrictId = '';
    this.states = await this.remedinovaData.getAllStates();
    // Filter states by country if needed
  }

  async onStateChange() {
    this.selectedDistrictId = '';
    this.districts = await this.remedinovaData.getDistrictsByState(this.selectedStateId);
  }

  async onDistrictChange() {
    // Load blocks if needed
    const blocks = await this.remedinovaData.getBlocksByDistrict(this.selectedDistrictId);
    console.log('Available blocks:', blocks);
  }
}

// Example 4: Medicine Search Component
@Component({
  selector: 'app-medicine-search',
  template: `
    <ion-searchbar 
      [(ngModel)]="searchText" 
      (ionInput)="onSearch()" 
      placeholder="Search medicines..."
      debounce="500">
    </ion-searchbar>
    
    <ion-list *ngIf="searchResults.length > 0">
      <ion-item *ngFor="let medicine of searchResults" button (click)="selectMedicine(medicine)">
        <ion-label>
          <h3>{{ medicine.Medicine_Name }}</h3>
          <p>Class: {{ medicine.Drug_Class_Id }} | Form: {{ medicine.Drug_Form_Id }}</p>
        </ion-label>
      </ion-item>
    </ion-list>
    
    <div *ngIf="searchText && searchResults.length === 0" class="no-results">
      <p>No medicines found for "{{ searchText }}"</p>
    </div>
  `
})
export class MedicineSearchComponent {
  searchText = '';
  searchResults: Medicine[] = [];

  constructor(private remedinovaData: RemedinovaDataService) {}

  async onSearch() {
    if (this.searchText.trim().length >= 2) {
      this.searchResults = await this.remedinovaData.searchMedicines(this.searchText);
    } else {
      this.searchResults = [];
    }
  }

  selectMedicine(medicine: Medicine) {
    console.log('Selected medicine:', medicine);
    // Handle selection
  }
}

// Example 5: Data Statistics Component
@Component({
  selector: 'app-data-stats',
  template: `
    <ion-card>
      <ion-card-header>
        <ion-card-title>Database Statistics</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="6" *ngFor="let stat of stats">
              <div class="stat-item">
                <h3>{{ stat.count }}</h3>
                <p>{{ stat.name }}</p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
        
        <ion-button (click)="refreshStats()" fill="outline" size="small">
          <ion-icon name="refresh-outline" slot="start"></ion-icon>
          Refresh
        </ion-button>
      </ion-card-content>
    </ion-card>
  `
})
export class DataStatsComponent implements OnInit {
  stats: { name: string; count: number }[] = [];

  constructor(private remedinovaData: RemedinovaDataService) {}

  async ngOnInit() {
    await this.loadStats();
  }

  async loadStats() {
    const dbStats = await this.remedinovaData.getDatabaseStats();
    
    this.stats = [
      { name: 'Complaints', count: dbStats['tblpatientcomplaints']?.doc_count || 0 },
      { name: 'Medicines', count: dbStats['tblmedicinemaster']?.doc_count || 0 },
      { name: 'Diagnoses', count: dbStats['tbldiagnosismaster']?.doc_count || 0 },
      { name: 'States', count: dbStats['tblstate']?.doc_count || 0 },
      { name: 'Districts', count: dbStats['tbldistrict']?.doc_count || 0 },
      { name: 'Lab Tests', count: dbStats['tbllabsubtest']?.doc_count || 0 }
    ];
  }

  async refreshStats() {
    await this.loadStats();
  }
}
