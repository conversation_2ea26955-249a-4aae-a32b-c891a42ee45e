// src/app/demoretriv/demoretriv.page.ts
import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonSpinner
} from '@ionic/angular/standalone';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonSpinner  // <--- needed for <ion-spinner>
  ],
  template: `
<ion-header>
  <ion-toolbar>
    <ion-title>tblpatientcomplaints</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="loading(); else dataTpl" style="display:flex; gap:8px; align-items:center;">
    <ion-spinner name="crescent"></ion-spinner>
    <div>Loading complaints...</div>
  </div>

  <ng-template #dataTpl>
    <div *ngIf="!rows().length">
      <div>No complaints found.</div>
    </div>

    <div *ngIf="rows().length" style="overflow:auto;">
      <table style="border-collapse:collapse; width:100%; font-size:12px;">
        <thead>
          <tr>
            <th *ngFor="let col of columns()" style="border:1px solid #ccc; padding:6px; background:#f0f0f0; text-align:left;">
              {{ col }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let r of rows(); let i = index">
            <td *ngFor="let col of columns()" style="border:1px solid #ddd; padding:6px; vertical-align:top;">
              {{ format(r[col]) }}
            </td>
          </tr>
        </tbody>
      </table>
      <div style="margin-top:8px;">Total rows: {{ rows().length }}</div>
    </div>
  </ng-template>
</ion-content>
  `,
})
export class DemoretrivPage implements OnInit {

}
