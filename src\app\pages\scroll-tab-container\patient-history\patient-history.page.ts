
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Component, OnInit, ChangeDetectorRef, NgZone } from '@angular/core';
import { DbService } from '../../../services/db.service';

@Component({
  selector: 'app-patient-history',
  templateUrl: './patient-history.page.html',
  styleUrls: ['./patient-history.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule]
})
export class PatientHistoryPage implements OnInit {


   selectedTab: string = 'patientHistory';

   constructor(
  private ngZone: NgZone,
  private cdRef: ChangeDetectorRef,
  private dbService: DbService
) {}

  ngOnInit() {
    // Optional: Initialization logic here
  }
switchTab(tab: string) {
  this.ngZone.run(() => {
    this.selectedTab = tab;
    this.cdRef.detectChanges();

    // Update active class on buttons
    setTimeout(() => {
      const buttons = document.querySelectorAll('.button-native');
      buttons.forEach(btn => btn.classList.remove('active'));

      const activeButton = document.querySelector(`[onclick*="${tab}"]`) ||
                          Array.from(buttons).find(btn =>
                            btn.textContent?.toLowerCase().replace(/\s+/g, '') ===
                            tab.toLowerCase().replace(/([A-Z])/g, ' $1').trim().replace(/\s+/g, '')
                          );

      if (activeButton) {
        activeButton.classList.add('active');
      }
    }, 10);
  });
}

// ----------------- save data in db --------------
 formData = {
    presentIllness: '',
    personalHistory: '',
    pastHistory1: '',
    pastHistory2: '',
    familyHistory: '',
    medications: '',
    medicalAllergies: '',
    otherAllergies: '',
    additionalNotes: '',
    physicalExam: '',
    reviewNote: ''
  };

  async savePatientHistory() {
    try {
      await this.dbService.addPatientHistory(this.formData);
      alert('Patient History saved successfully!');
      this.formData = {
        presentIllness: '',
        personalHistory: '',
        pastHistory1: '',
        pastHistory2: '',
        familyHistory: '',
        medications: '',
        medicalAllergies: '',
        otherAllergies: '',
        additionalNotes: '',
        physicalExam: '',
        reviewNote: ''
      };

      //  Log all saved patientHistory records to console
    const result = await this.dbService.getPatientHistory();
    console.log('All patient history records:', result.docs);

    } catch (err) {
      console.error('Error saving:', err);
      alert('Failed to save');
    }
  }
}
