import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },

  // RemediNova Data Table Routes
  {
    path: 'table-navigator',
    loadComponent: () => import('./pages/table-navigator/table-navigator.page').then(m => m.TableNavigatorPage)
  },
  {
    path: 'data-tables',
    loadComponent: () => import('./pages/data-tables/data-tables.page').then(m => m.DataTablesPage)
  },
  {
    path: 'all-tables-view',
    loadComponent: () => import('./pages/all-tables-view/all-tables-view.page').then(m => m.AllTablesViewPage)
  },
  {
    path: 'data-demo',
    loadComponent: () => import('./pages/data-demo/data-demo.page').then(m => m.DataDemoPage)
  },

  // Existing routes
  {
    path: 'navbar',
    loadComponent: () => import('./pages/navbar/navbar.page').then( m => m.NavbarPage)
  },
  {
    path: 'sidebar',
    loadComponent: () => import('./pages/sidebar/sidebar.page').then( m => m.SidebarPage)
  },
  {
    path: 'patientinfo',
    loadComponent: () => import('./pages/patientinfo/patientinfo.page').then( m => m.PatientinfoPage)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./pages/dashboard/dashboard.page').then( m => m.DashboardPage)
  },
  {
    path: 'tabs',
    loadComponent: () => import('./pages/scroll-tab-container/tabs/tabs.page').then( m => m.TabsPage)
  },
  {
    path: 'patient-history',
    loadComponent: () => import('./pages/scroll-tab-container/patient-history/patient-history.page').then( m => m.PatientHistoryPage)
  },
];
