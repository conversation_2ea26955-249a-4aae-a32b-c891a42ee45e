// src/app/services/db.service.ts
import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import PouchFind from 'pouchdb-find';

PouchDB.plugin(PouchFind);

@Injectable({
  providedIn: 'root'
})
export class DbService {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('consultation_db'); // 1 DB for all "tables"
    console.log('PouchDB initialized');
  }

  addPatientHistory(data: any) {
    const doc = {
      _id: new Date().toISOString(),
      type: 'patientHistory',
      ...data
    };
    return this.db.put(doc);
  }

getPatientHistory() {
  return this.db.find({
    selector: { type: 'patientHistory' }
  });
}
}
