<ion-content class="home-container" fullscreen scroll-y="true">

  <!-- Loading State -->
  <div *ngIf="isDataLoading" class="loading-container">
    <ion-spinner name="crescent" color="primary"></ion-spinner>
    <h2>Loading RemediNova Database</h2>
    <p>Please wait while we initialize your medical data...</p>
    <ion-progress-bar type="indeterminate" color="primary"></ion-progress-bar>

    <!-- Show diagnostic info if available -->
    <div *ngIf="diagnosticInfo" class="diagnostic-info">
      <p><strong>Tables:</strong> {{ diagnosticInfo.configuredTables || 0 }}</p>
      <p><strong>Records:</strong> {{ diagnosticInfo.totalRecords || 0 | number }}</p>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="dataLoadError" class="error-container">
    <ion-icon name="warning-outline" color="danger" class="error-icon"></ion-icon>
    <h2>Failed to Load Database</h2>
    <p>{{ errorMessage }}</p>

    <div class="error-actions">
      <ion-button (click)="retryDataLoad()" color="primary">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        Retry
      </ion-button>

      <ion-button (click)="runQuickTest()" fill="outline" color="secondary">
        <ion-icon name="bug-outline" slot="start"></ion-icon>
        Run Test
      </ion-button>
    </div>

    <!-- Debug info -->
    <div class="debug-info" *ngIf="diagnosticInfo || startupResult">
      <h4>Debug Information:</h4>
      <div *ngIf="diagnosticInfo">
        <p><small>JSON File Size: {{ diagnosticInfo.jsonFileSize | number }} bytes</small></p>
        <p><small>Configured Tables: {{ diagnosticInfo.configuredTables }}</small></p>
        <p><small>Data Array: {{ diagnosticInfo.hasDataArray ? 'Found' : 'Missing' }}</small></p>
      </div>
      <div *ngIf="startupResult">
        <p><small>Init Time: {{ startupResult.details?.totalInitTime }}ms</small></p>
        <p *ngIf="hasWarnings()"><small>Warnings: {{ startupResult.warnings?.length }}</small></p>
      </div>

      <!-- Error details -->
      <div *ngIf="getErrorDetails().length > 0" class="error-details">
        <h5>Error Details:</h5>
        <ul>
          <li *ngFor="let error of getErrorDetails()">{{ error }}</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isDataLoading && !dataLoadError">
    <!-- Success indicator -->
    <div class="success-banner">
      <ion-icon name="checkmark-circle" color="success"></ion-icon>
      <span>Database Ready</span>
      <span *ngIf="diagnosticInfo" class="stats">
        {{ diagnosticInfo.loadedTables }} tables • {{ diagnosticInfo.totalRecords | number }} records
      </span>
    </div>

    <app-table-navigator></app-table-navigator>
  </div>

</ion-content>
