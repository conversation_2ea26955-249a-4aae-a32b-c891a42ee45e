<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>RemediNova Data Demo</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="reloadAllData()" [disabled]="isLoading">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">RemediNova Data Demo</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading data...</p>
  </div>

  <div *ngIf="!isLoading">
    <!-- Database Statistics -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Database Statistics</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="6">
              <ion-item>
                <ion-label>
                  <h3>Total Documents</h3>
                  <p>{{ getTotalDocuments() }}</p>
                </ion-label>
              </ion-item>
            </ion-col>
            <ion-col size="6">
              <ion-item>
                <ion-label>
                  <h3>Total Tables</h3>
                  <p>{{ Object.keys(dbStats).length }}</p>
                </ion-label>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-grid>
        
        <!-- Individual table stats -->
        <ion-list>
          <ion-item *ngFor="let tableName of Object.keys(dbStats)">
            <ion-label>
              <h3>{{ tableName }}</h3>
              <p>{{ getItemCount(tableName) }} documents</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Search Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Search Data</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- Table Selection -->
        <ion-item>
          <ion-label>Search in:</ion-label>
          <ion-select [(ngModel)]="selectedTable" (ionChange)="onTableChange()">
            <ion-select-option value="complaints">Patient Complaints</ion-select-option>
            <ion-select-option value="medicines">Medicines</ion-select-option>
            <ion-select-option value="diagnoses">Diagnoses</ion-select-option>
            <ion-select-option value="instructions">Instructions</ion-select-option>
            <ion-select-option value="labtests">Lab Tests</ion-select-option>
          </ion-select>
        </ion-item>

        <!-- Search Input -->
        <ion-item>
          <ion-label position="stacked">Search Text</ion-label>
          <ion-input 
            [(ngModel)]="searchText" 
            (ionInput)="onSearch()" 
            placeholder="Enter search term..."
            debounce="300">
          </ion-input>
        </ion-item>

        <!-- Search Results -->
        <div *ngIf="searchResults.length > 0">
          <h4>Search Results ({{ searchResults.length }})</h4>
          <ion-list>
            <ion-item *ngFor="let result of searchResults">
              <ion-label>
                <h3>{{ getSearchResultDisplayText(result) }}</h3>
                <p *ngIf="result.Id">ID: {{ result.Id }}</p>
                <p *ngIf="result.ReMeDi_Code">Code: {{ result.ReMeDi_Code }}</p>
                <p *ngIf="result.ICD_CODE">ICD Code: {{ result.ICD_CODE }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <div *ngIf="searchText && searchResults.length === 0 && !isLoading">
          <p>No results found for "{{ searchText }}" in {{ getTableDisplayName(selectedTable) }}</p>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Sample Data Display -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Sample Data</ion-card-title>
        <ion-card-subtitle>First 10 items from each table</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <!-- Patient Complaints -->
        <div *ngIf="complaints.length > 0">
          <h4>Patient Complaints</h4>
          <ion-list>
            <ion-item *ngFor="let complaint of complaints">
              <ion-label>
                <h3>{{ complaint.ComplaintName }}</h3>
                <p>ID: {{ complaint.Id }} | Code: {{ complaint.ReMeDi_Code || 'N/A' }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Medicines -->
        <div *ngIf="medicines.length > 0">
          <h4>Medicines</h4>
          <ion-list>
            <ion-item *ngFor="let medicine of medicines">
              <ion-label>
                <h3>{{ medicine.Medicine_Name }}</h3>
                <p>ID: {{ medicine.Medicine_Id }} | Class: {{ medicine.Drug_Class_Id }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Diagnoses -->
        <div *ngIf="diagnoses.length > 0">
          <h4>Diagnoses</h4>
          <ion-list>
            <ion-item *ngFor="let diagnosis of diagnoses">
              <ion-label>
                <h3>{{ diagnosis.Diagnosis_Name }}</h3>
                <p>ID: {{ diagnosis.Dignosis_Id }} | ICD: {{ diagnosis.ICD_CODE }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- States -->
        <div *ngIf="states.length > 0">
          <h4>States</h4>
          <ion-list>
            <ion-item *ngFor="let state of states">
              <ion-label>
                <h3>{{ state.State }}</h3>
                <p>ID: {{ state.StateId }} | Country: {{ state.CountryId }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Countries -->
        <div *ngIf="countries.length > 0">
          <h4>Countries</h4>
          <ion-list>
            <ion-item *ngFor="let country of countries">
              <ion-label>
                <h3>{{ country.Country }}</h3>
                <p>ID: {{ country.CountryId }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
