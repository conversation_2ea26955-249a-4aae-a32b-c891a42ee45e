<div class="patient-history-section">
  <h3>Patient History</h3>

  <div class="history-grid">
    <div class="field-block">
      <label>History of Present Illness *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.presentIllness"></textarea>
    </div>

    <div class="field-block">
      <label>Personal History *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.personalHistory"></textarea>
    </div>

    <div class="field-block">
      <label>Past Medical or Surgical History *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.pastHistory1"></textarea>
    </div>

    <div class="field-block">
      <label>Past Medical or Surgical History *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.pastHistory2"></textarea>
    </div>

    <div class="field-block">
      <label>Family History *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.familyHistory"></textarea>
    </div>

    <div class="field-block">
      <label>Current And Recent Medications *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.medications"></textarea>
    </div>

    <div class="field-block">
      <label>Medical Allergies *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.medicalAllergies"></textarea>
    </div>

    <div class="field-block">
      <label>Other Allergies Or Sensitivities *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.otherAllergies"></textarea>
    </div>

    <div class="field-block">
      <label>Additional Notes *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.additionalNotes"></textarea>
    </div>

    <div class="field-block">
      <label>Physical Examination *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.physicalExam"></textarea>
    </div>

    <div class="field-block">
      <label>Review Note *</label>
      <textarea placeholder="Text area" [(ngModel)]="formData.reviewNote"></textarea>
    </div>
  </div>

  <button (click)="savePatientHistory()" style=" align-items: flex-end; margin-bottom :30px; margin-top: 39px; font-size: 14px; color: #f9fafc; font-weight: 600; background: #007AFF; border: none; cursor: pointer; padding: 10px 20px; border-radius: 8px;">Add</button>
</div>
